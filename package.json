{"name": "upbase", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18", "react-dom": "^18", "next": "14.2.5", "@supabase/supabase-js": "^2.45.0", "@supabase/ssr": "^0.4.0", "zustand": "^4.5.4", "recharts": "^2.12.7", "date-fns": "^3.6.0", "lucide-react": "^0.427.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-calendar": "^1.1.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "eslint": "^8", "eslint-config-next": "14.2.5"}}